<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> <PERSON><PERSON> - Senior Scrum Master</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            line-height: 1.4;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .shadow-custom { box-shadow: 0 10px 25px rgba(0,0,0,0.1); }

        /* A4 PDF Optimization */
        @media print {
            body {
                font-size: 12px;
                line-height: 1.4;
                margin: 0;
                padding: 0;
            }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
            .avoid-break { page-break-inside: avoid; }
            .shadow-custom { box-shadow: none; }

            /* Force desktop layout in print mode */
            .print-desktop-flex {
                display: flex !important;
                flex-direction: row !important;
                align-items: center !important;
                gap: 0.75rem !important;
            }

            .print-desktop-grid {
                display: grid !important;
                grid-template-columns: 2fr 1fr !important;
                gap: 0.75rem !important;
            }

            .print-desktop-justify-between {
                display: flex !important;
                justify-content: space-between !important;
                align-items: flex-start !important;
            }

            .print-desktop-text-left {
                text-align: left !important;
            }

            .print-desktop-justify-start {
                justify-content: flex-start !important;
            }

            .print-desktop-col-span-2 {
                grid-column: span 2 / span 2 !important;
            }
        }

        /* A4 Layout */
        .a4-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            box-sizing: border-box;
            padding: 0;
        }

        @media screen {
            .a4-container {
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin: 20px auto;
            }
        }

        /* Custom text sizes for A4 - Increased for better readability */
        .text-header { font-size: 32px; font-weight: 700; }
        .text-subheader { font-size: 22px; font-weight: 400; }
        .text-section { font-size: 18px; font-weight: 600; }
        .text-subsection { font-size: 14px; font-weight: 600; }
        .text-content { font-size: 14px; font-weight: 400; }
        .text-body { font-size: 14px; font-weight: 400; }
        .text-small { font-size: 13px; font-weight: 400; }
        .text-tiny { font-size: 10px; font-weight: 400; }

        @media print {
            .text-header { font-size: 32px; }
            .text-subheader { font-size: 22px; }
            .text-section { font-size: 18px; }
            .text-subsection { font-size: 14px; }
            .text-content { font-size: 14px; }
            .text-body { font-size: 14px; }
            .text-small { font-size: 13px; }
            .text-tiny { font-size: 10px; }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">
    <div class="a4-container">
        <!-- Header Section -->
        <header class="gradient-bg text-white p-4">
            <div class="flex flex-col md:flex-row print-desktop-flex items-center gap-3">
                <!-- Profile Image -->
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm flex-shrink-0">
                    <i data-lucide="user" class="w-8 h-8 text-white/80"></i>
                </div>

                <!-- Name and Title -->
                <div class="text-center md:text-left print-desktop-text-left flex-1">
                    <h1 class="text-header font-bold mb-1">Phạm Thị Ngân</h1>
                    <h2 class="text-subheader font-light opacity-90 mb-2">Senior Scrum Master</h2>

                    <!-- Contact Info -->
                    <div class="flex flex-wrap justify-center md:justify-start print-desktop-justify-start gap-4 text-body">
                        <div class="flex items-center gap-2">
                            <i data-lucide="phone" class="w-5 h-5"></i>
                            <span>0396 884 092</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i data-lucide="mail" class="w-5 h-5"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i data-lucide="map-pin" class="w-5 h-5"></i>
                            <span>TP. Hà Nội</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 print-desktop-grid gap-3 p-3">
            <!-- Left Column - Main Content -->
            <div class="lg:col-span-2 print-desktop-col-span-2 space-y-3">
                <!-- Summary -->
                <section class="avoid-break">
                    <h3 class="text-section font-bold text-gray-800 mb-2 border-b-2 border-indigo-500 pb-1">
                        <i data-lucide="user-check" class="w-4 h-4 inline mr-1 text-indigo-600"></i>
                        Tóm Tắt Chuyên Môn
                    </h3>
                    <div class="bg-gradient-to-r from-indigo-50 to-blue-50 p-3 rounded-lg">
                        <p class="text-gray-700 leading-relaxed mb-2 text-content">
                            Scrum Master dày dạn kinh nghiệm với hơn <strong class="text-indigo-600">8 năm</strong> dẫn dắt các dự án phần mềm phức tạp trong lĩnh vực <strong>E-commerce và Logistics</strong>.
                            Chuyên gia triển khai và tối ưu hóa quy trình Agile, đã dẫn dắt thành công trên <strong class="text-indigo-600">10 đội nhóm</strong>, giúp cải thiện năng suất và rút ngắn thời gian ra mắt sản phẩm.
                        </p>
                        <p class="text-gray-700 leading-relaxed text-content">
                            Có kinh nghiệm trong vai trò <strong>Lead Scrum Master</strong>, cố vấn và phát triển các Scrum Master khác.
                            Kỹ năng nổi bật về <strong>coaching, giải quyết xung đột</strong> và xây dựng <strong>văn hóa cải tiến liên tục</strong>.
                        </p>
                    </div>
                </section>

                <!-- Experience -->
                <section>
                    <h3 class="text-section font-bold text-gray-800 mb-2 border-b-2 border-indigo-500 pb-1">
                        <i data-lucide="briefcase" class="w-4 h-4 inline mr-1 text-indigo-600"></i>
                        Kinh Nghiệm Làm Việc
                    </h3>

                    <!-- Current Position -->
                    <div class="mb-3 bg-gradient-to-r from-indigo-50 to-purple-50 p-2 rounded-lg border-l-4 border-indigo-500 avoid-break">
                        <div class="flex flex-col md:flex-row print-desktop-justify-between md:justify-between md:items-start mb-1">
                            <div>
                                <h4 class="text-subsection font-bold text-gray-800">LEAD SCRUM MASTER</h4>
                                <p class="text-indigo-600 font-semibold text-body">Công ty TNHH Giải pháp Công nghệ Gobiz</p>
                            </div>
                            <span class="text-small bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full mt-1 md:mt-0 font-semibold">01/2021 - Hiện tại</span>
                        </div>
                        <p class="text-gray-600 italic mb-3 text-body">Với vai trò Lead Scrum Master, tôi chịu trách nhiệm dẫn dắt các team phát triển sản phẩm chủ lực của công ty, đồng thời cố vấn cho các Scrum Master khác.</p>
                        <ul class="space-y-3 text-gray-700 text-body">
                            <li class="flex items-start gap-3">
                                <i data-lucide="target" class="w-5 h-5 text-indigo-500 mt-0.5 flex-shrink-0"></i>
                                <span>Dẫn dắt và điều phối đồng thời lên đến <strong class="text-indigo-600">4 Scrum team</strong>, phát triển các sản phẩm phức tạp về TMĐT, hệ thống đặt hàng và quản lý kho vận</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="users" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"></i>
                                <span>Huấn luyện và cố vấn cho <strong class="text-green-600">2 Scrum Master</strong> khác, giúp họ nâng cao năng lực quản lý đội nhóm và giải quyết các vấn đề hiệu quả</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="trending-up" class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0"></i>
                                <span>Triển khai thành công mô hình <strong class="text-blue-600">Scrumban</strong> trong giai đoạn mở rộng sản phẩm, giúp tối ưu luồng công việc và tăng khả năng đáp ứng với các thay đổi của thị trường</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="bar-chart" class="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0"></i>
                                <span>Xây dựng <strong class="text-purple-600">hệ thống báo cáo sprint tự động</strong> và theo dõi velocity, giúp ban lãnh đạo có cái nhìn trực quan về tiến độ và hiệu suất của các dự án</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="dollar-sign" class="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0"></i>
                                <span>Tham gia vào <strong class="text-orange-600">quản lý dự án</strong>, trực tiếp kiểm soát chi phí nhân sự và tối ưu hóa việc phân bổ nguồn lực giữa các team</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Previous Position 1 -->
                    <div class="mb-3 bg-gray-50 p-3 rounded-lg border-l-4 border-gray-400 avoid-break">
                        <div class="flex flex-col md:flex-row print-desktop-justify-between md:justify-between md:items-start mb-2">
                            <div>
                                <h4 class="text-sm font-bold text-gray-800">SCRUM MASTER</h4>
                                <p class="text-gray-600 font-semibold text-xs">Công ty CP DV Chuyển phát Thông minh</p>
                            </div>
                            <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full mt-1 md:mt-0 font-semibold">07/2017 - 12/2020</span>
                        </div>
                        <ul class="space-y-3 text-gray-700 text-body">
                            <li class="flex items-start gap-3">
                                <i data-lucide="smartphone" class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0"></i>
                                <span>Dẫn dắt <strong class="text-blue-600">2 đội nhóm</strong> phát triển hệ thống quản lý giao vận nội bộ (Web & Mobile App)</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="refresh-cw" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"></i>
                                <span>Huấn luyện (Coaching) thành công cho các đội nhóm trong quá trình chuyển đổi từ mô hình <strong class="text-green-600">Waterfall sang Scrum</strong>, giúp team nhanh chóng thích nghi và đạt được hiệu suất cao</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="list-checks" class="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0"></i>
                                <span>Phối hợp chặt chẽ với Product Owner và BA để xây dựng, quản lý và tối ưu hóa <strong class="text-purple-600">Product Backlog</strong>, đảm bảo các hạng mục luôn rõ ràng và được sắp xếp ưu tiên hợp lý</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Previous Position 2 -->
                    <div class="mb-3 bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-400 avoid-break">
                        <div class="flex flex-col md:flex-row print-desktop-justify-between md:justify-between md:items-start mb-2">
                            <div>
                                <h4 class="text-sm font-bold text-gray-800">DEV FRONTEND kiêm nhiệm SCRUM MASTER</h4>
                                <p class="text-gray-600 font-semibold text-xs">Công ty CP DV Chuyển phát Thông minh</p>
                            </div>
                            <span class="text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded-full mt-1 md:mt-0 font-semibold">01/2017 - 06/2017</span>
                        </div>
                        <div class="bg-yellow-100 p-2 rounded-lg mb-2">
                            <h5 class="font-bold text-yellow-800 mb-1 text-xs">🏆 THÀNH TỰU NỔI BẬT:</h5>
                            <p class="text-yellow-700 text-small">Là người tiên phong đề xuất và triển khai thử nghiệm thành công mô hình Scrum đầu tiên tại công ty. Kết quả tích cực của dự án đã thuyết phục ban lãnh đạo quyết định nhân rộng mô hình Agile cho toàn bộ khối phát triển sản phẩm.</p>
                        </div>
                        <ul class="space-y-3 text-gray-700 text-body">
                            <li class="flex items-start gap-3">
                                <i data-lucide="code" class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0"></i>
                                <span>Vừa phát triển giao diện (HTML/CSS/jQuery), vừa đảm nhiệm vai trò Scrum Master cho team <strong>5 thành viên</strong></span>
                            </li>
                        </ul>
                    </div>

                    <!-- Previous Position 3 -->
                    <div class="mb-3 bg-gray-50 p-3 rounded-lg border-l-4 border-gray-300 avoid-break">
                        <div class="flex flex-col md:flex-row print-desktop-justify-between md:justify-between md:items-start mb-2">
                            <div>
                                <h4 class="text-sm font-bold text-gray-800">DEV FRONTEND</h4>
                                <p class="text-gray-600 font-semibold text-xs">Công ty CP Truyền thông và Công nghệ Alimama Việt Nam</p>
                            </div>
                            <span class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full mt-1 md:mt-0 font-semibold">11/2015 - 09/2016</span>
                        </div>
                        <ul class="space-y-3 text-gray-700 text-body">
                            <li class="flex items-start gap-3">
                                <i data-lucide="shopping-cart" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"></i>
                                <span>Phát triển giao diện web cho các dự án <strong>TMĐT</strong> bằng HTML5, CSS3, JavaScript và jQuery</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="layers" class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0"></i>
                                <span>Xây dựng các <strong>component UI có thể tái sử dụng</strong> và đảm bảo thiết kế responsive trên đa nền tảng</span>
                            </li>
                        </ul>
                    </div>
                </section>

                <!-- Featured Projects -->
                <section class="page-break">
                    <h3 class="text-lg font-bold text-gray-800 mb-3 border-b-2 border-indigo-500 pb-1">
                        <i data-lucide="rocket" class="w-4 h-4 inline mr-1 text-indigo-600"></i>
                        Dự Án Tiêu Biểu
                    </h3>

                    <!-- Project 1 -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg mb-4 border-l-4 border-blue-500 avoid-break">
                        <div class="flex flex-col md:flex-row print-desktop-justify-between md:justify-between md:items-start mb-2">
                            <div>
                                <h4 class="text-sm font-bold text-gray-800 mb-1">Sabomall.com - Nền tảng TMĐT đa ngành hàng</h4>
                                <p class="text-indigo-600 font-semibold mb-1 text-xs">Vai trò: Scrum Master & Release Manager</p>
                            </div>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-semibold">09/2023 - 01/2025</span>
                        </div>

                        <p class="text-gray-700 mb-2 text-small">Điều phối <strong class="text-blue-600">2 Scrum team (18 thành viên)</strong> phát triển nền tảng từ ý tưởng đến khi ra mắt.</p>

                        <div class="bg-white p-3 rounded-lg mb-2">
                            <h5 class="font-bold text-gray-800 mb-2 text-small">🎯 KẾT QUẢ ĐẠT ĐƯỢC:</h5>
                            <div class="grid grid-cols-3 gap-2 mb-3">
                                <div class="text-center p-2 bg-green-50 rounded-lg">
                                    <div class="text-xl font-bold text-green-600">95%</div>
                                    <div class="text-tiny text-gray-600">Tỷ lệ hoàn thành Sprint</div>
                                </div>
                                <div class="text-center p-2 bg-blue-50 rounded-lg">
                                    <div class="text-xl font-bold text-blue-600">3</div>
                                    <div class="text-tiny text-gray-600">Tháng phát triển</div>
                                </div>
                                <div class="text-center p-2 bg-purple-50 rounded-lg">
                                    <div class="text-xl font-bold text-purple-600">Top 10</div>
                                    <div class="text-tiny text-gray-600">Ứng dụng phổ biến</div>
                                </div>
                            </div>
                            <ul class="space-y-3 text-gray-700 text-small">
                                <li class="flex items-start gap-3">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"></i>
                                    <span>Ra mắt thành công sản phẩm chỉ sau <strong>3 tháng phát triển</strong></span>
                                </li>
                                <li class="flex items-start gap-3">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"></i>
                                    <span>Sau 4 tháng, ứng dụng lọt <strong>top 10 ứng dụng mua sắm</strong> được cài đặt nhiều nhất trong tháng</span>
                                </li>
                                <li class="flex items-start gap-3">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"></i>
                                    <span>Áp dụng thành công <strong>Release Train và mô hình Scrumban</strong>, giúp tối ưu hóa quy trình và giảm thời gian đưa tính năng mới ra thị trường</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Project 2 -->
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border-l-4 border-green-500 avoid-break">
                        <div class="flex flex-col md:flex-row print-desktop-justify-between md:justify-between md:items-start mb-2">
                            <div>
                                <h4 class="text-sm font-bold text-gray-800 mb-1">Chatbot AI Mua hàng</h4>
                                <p class="text-emerald-600 font-semibold mb-1 text-xs">Vai trò: Scrum Master</p>
                            </div>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-semibold">03/2025 - Hiện tại</span>
                        </div>

                        <p class="text-gray-700 mb-2 text-xs">Dẫn dắt team <strong class="text-green-600">5 thành viên</strong> theo mô hình Kanban phát triển hệ thống Chatbot AI đặt hàng từ các sàn TMĐT Trung Quốc.</p>

                        <div class="bg-white p-2 rounded-lg">
                            <h5 class="font-bold text-gray-800 mb-2 text-xs">🎯 KẾT QUẢ ĐẠT ĐƯỢC:</h5>
                            <div class="text-center p-2 bg-emerald-50 rounded-lg mb-2">
                                <div class="text-2xl font-bold text-emerald-600">65%</div>
                                <div class="text-xs text-gray-600">Khối lượng đơn hàng được xử lý tự động so với giao dịch viên truyền thống</div>
                            </div>
                            <ul class="space-y-1 text-gray-700 text-xs">
                                <li class="flex items-start gap-1">
                                    <i data-lucide="check-circle" class="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0"></i>
                                    <span>Hệ thống chatbot xử lý thành công <strong>65% khối lượng đơn hàng</strong>, giúp tối ưu hóa nguồn lực vận hành</span>
                                </li>
                                <li class="flex items-start gap-1">
                                    <i data-lucide="check-circle" class="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0"></i>
                                    <span>Xây dựng quy trình <strong>thu thập phản hồi người dùng</strong> và cải tiến sản phẩm liên tục, đảm bảo chatbot ngày càng thông minh và hiệu quả</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Right Column - Sidebar -->
            <div class="space-y-3">
                <!-- Skills -->
                <section class="bg-gradient-to-br from-indigo-50 to-purple-50 p-3 rounded-lg border border-indigo-200">
                    <h3 class="text-section font-bold text-gray-800 mb-2">
                        <i data-lucide="settings" class="w-4 h-4 inline mr-1 text-indigo-600"></i>
                        Kỹ Năng Nổi Bật
                    </h3>

                    <div class="space-y-4">
                        <div>
                            <h4 class="font-bold text-gray-700 mb-3 text-small uppercase tracking-wide">🚀 Agile Frameworks</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-small font-semibold">Scrum</span>
                                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-small font-semibold">Kanban</span>
                                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-small font-semibold">Scrumban</span>
                                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-small font-semibold">LeSS</span>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-700 mb-3 text-small uppercase tracking-wide">📋 Điều phối sự kiện Scrum</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-tiny font-semibold">Sprint Planning</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-tiny font-semibold">Daily Stand-up</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-tiny font-semibold">Sprint Review</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-tiny font-semibold">Retrospective</span>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-700 mb-3 text-small uppercase tracking-wide">👥 Huấn luyện & Phát triển</h4>
                            <ul class="text-small text-gray-700 space-y-2">
                                <li class="flex items-center gap-2">
                                    <i data-lucide="users" class="w-4 h-4 text-green-500"></i>
                                    <span>Coaching đội nhóm</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i data-lucide="user-plus" class="w-4 h-4 text-blue-500"></i>
                                    <span>Mentoring Scrum Master</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i data-lucide="trending-up" class="w-4 h-4 text-purple-500"></i>
                                    <span>Xây dựng văn hóa Agile</span>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-700 mb-3 text-small uppercase tracking-wide">⚙️ Quản lý & Tối ưu</h4>
                            <ul class="text-small text-gray-700 space-y-2">
                                <li class="flex items-center gap-2">
                                    <i data-lucide="list-checks" class="w-4 h-4 text-orange-500"></i>
                                    <span>Quản lý Backlog</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i data-lucide="shield-alert" class="w-4 h-4 text-red-500"></i>
                                    <span>Loại bỏ rào cản (Impediment Removal)</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i data-lucide="zap" class="w-4 h-4 text-yellow-500"></i>
                                    <span>Tối ưu hóa quy trình</span>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-700 mb-3 text-small uppercase tracking-wide">🛠️ Công cụ</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-small font-semibold">Jira</span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-small font-semibold">Trello</span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-small font-semibold">Asana</span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-small font-semibold">Taiga</span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-small font-semibold">Notion</span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-small font-semibold">Base.vn</span>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-700 mb-3 text-small uppercase tracking-wide">💼 Kỹ năng khác</h4>
                            <ul class="text-small text-gray-700 space-y-2">
                                <li class="flex items-center gap-2">
                                    <i data-lucide="handshake" class="w-4 h-4 text-indigo-500"></i>
                                    <span>Quản lý các bên liên quan</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i data-lucide="lightbulb" class="w-4 h-4 text-yellow-500"></i>
                                    <span>Giải quyết vấn đề</span>
                                </li>
                                <li class="flex items-center gap-2">
                                    <i data-lucide="dollar-sign" class="w-4 h-4 text-green-500"></i>
                                    <span>Quản lý dự án (Phân bổ nguồn lực, Chi phí)</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Certifications -->
                <section class="bg-gradient-to-br from-yellow-50 to-orange-50 p-3 rounded-lg border border-yellow-200">
                    <h3 class="text-section font-bold text-gray-800 mb-2">
                        <i data-lucide="award" class="w-4 h-4 inline mr-1 text-orange-600"></i>
                        Chứng Chỉ & Học Vấn
                    </h3>

                    <div class="space-y-3">
                        <div>
                            <h4 class="font-bold text-gray-700 mb-2 text-small uppercase tracking-wide">🏆 Chứng chỉ chuyên môn</h4>
                            <ul class="space-y-2 text-small">
                                <li class="flex items-start gap-2 bg-white p-2 rounded-lg">
                                    <i data-lucide="medal" class="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0"></i>
                                    <div>
                                        <div class="font-semibold text-gray-800 text-small">Professional Scrum Master I (PSM I)</div>
                                        <div class="text-gray-600 text-tiny">Scrum.org</div>
                                    </div>
                                </li>
                                <li class="flex items-start gap-2 bg-white p-2 rounded-lg">
                                    <i data-lucide="book-open" class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0"></i>
                                    <div>
                                        <div class="font-semibold text-gray-800 text-small">Pragmatic Scrum</div>
                                        <div class="text-gray-600 text-tiny">Học viện Agile</div>
                                    </div>
                                </li>
                                <li class="flex items-start gap-2 bg-white p-2 rounded-lg">
                                    <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0"></i>
                                    <div>
                                        <div class="font-semibold text-gray-800 text-small">Nâng cao năng lực quản trị</div>
                                        <div class="text-gray-600 text-tiny">Tổ chức giáo dục đào tạo PTI</div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-bold text-gray-700 mb-2 text-small uppercase tracking-wide">🎓 Học vấn</h4>
                            <div class="bg-white p-2 rounded-lg">
                                <div class="flex items-start gap-2">
                                    <i data-lucide="graduation-cap" class="w-3 h-3 text-indigo-500 mt-0.5 flex-shrink-0"></i>
                                    <div>
                                        <h4 class="font-semibold text-gray-800 text-small">Kỹ sư Công nghệ thông tin</h4>
                                        <p class="text-gray-600 text-small">Đại học Mỏ - Địa chất</p>
                                        <p class="text-gray-500 text-small">2009 - 2014</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>
